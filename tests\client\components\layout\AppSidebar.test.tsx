import { render, screen } from '@testing-library/react';
import type React from 'react';

// ---------- mocks ----------
const mockUseLocation = jest.fn().mockReturnValue(['/dashboard', jest.fn()]);
jest.mock('wouter', () => ({
  Link: ({ children, href }: any) => <a href={href}>{children}</a>,
  useLocation: () => mockUseLocation(),
}));

jest.mock('@/context/AuthContext', () => ({
  __esModule: true,
  useAuth: () => ({ user: { id: 1, name: 'Tester', email: '<EMAIL>' } }),
}));

jest.mock('@/hooks/use-admin', () => ({
  __esModule: true,
  useAdmin: () => ({ isAdmin: false }),
}));

jest.mock('@/hooks/use-stats', () => ({
  __esModule: true,
  useStats: () => ({ stats: { emailsProcessed: 100 } }),
}));

jest.mock('@/hooks/use-mobile', () => ({
  __esModule: true,
  useIsMobile: () => false,
}));

// Silence Lucide icons
jest.mock('lucide-react', () => ({
  __esModule: true,
  Plus: () => <svg />,
  PanelLeft: () => <svg />,
  Inbox: () => <svg />,
  BarChart: () => <svg />,
  FileText: () => <svg />,
  Archive: () => <svg />,
  Trash2: () => <svg />,
  Clock: () => <svg />,
  Star: () => <svg />,
  Settings: () => <svg />,
}));

// Mock UI components
jest.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: any) => <div>{children}</div>,
  Tooltip: ({ children }: any) => <div>{children}</div>,
  TooltipTrigger: ({ children }: any) => <div>{children}</div>,
  TooltipContent: ({ children }: any) => <div>{children}</div>,
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span>{children}</span>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, className, ...props }: any) => <button className={className} {...props}>{children}</button>,
}));

jest.mock('@/components/ui/sheet', () => ({
  Sheet: ({ children }: any) => <div>{children}</div>,
  SheetContent: ({ children }: any) => <div>{children}</div>,
}));

jest.mock('@/components/ui/separator', () => ({
  Separator: () => <hr />,
}));

jest.mock('@/components/ui/progress', () => ({
  Progress: ({ value }: any) => <div>Progress: {value}%</div>,
}));

import AppSidebar from '@/components/layout/AppSidebar';


describe('AppSidebar', () => {
  it('renders primary navigation links', () => {
    render(<AppSidebar />);
    expect(screen.getByText('Inbox')).toBeInTheDocument();
    expect(screen.getByText('Priority Map')).toBeInTheDocument();
  });

  it('renders navigation links with correct hrefs', () => {
    render(<AppSidebar />);
    const inbox = screen.getByText('Inbox').closest('a');
    expect(inbox).toBeInTheDocument();
    expect(inbox).toHaveAttribute('href', '/dashboard');
  });

  it('renders compose button', () => {
    render(<AppSidebar />);
    expect(screen.getByText('Compose')).toBeInTheDocument();
    // Note: User name is displayed in AppHeader, not AppSidebar
  });
}); 
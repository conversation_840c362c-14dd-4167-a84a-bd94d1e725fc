/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  displayName: 'client',
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  roots: ['<rootDir>/tests/client'],
  testMatch: [
    "**/__tests__/**/*.+(ts|tsx|js)",
    "**/?(*.)+(spec|test).+(ts|tsx|js)"
  ],
  transform: {
    "^.+\\.(ts|tsx)$": ["ts-jest", {
      tsconfig: "tsconfig.jest.json",
      diagnostics: false
    }]
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/client/src/$1',
    '^@components/(.*)$': '<rootDir>/client/src/components/$1',
    '^@hooks/(.*)$': '<rootDir>/client/src/hooks/$1',
    '^@context/(.*)$': '<rootDir>/client/src/context/$1',
    '^@lib/(.*)$': '<rootDir>/client/src/lib/$1'
  },
  setupFilesAfterEnv: ['<rootDir>/tests/client/setupTests.ts'],
  // Automatically clear mock calls and instances between every test
  clearMocks: true,
  // Transform ESM modules that Jest can't handle by default
  transformIgnorePatterns: [
    'node_modules/(?!wouter)'
  ],
}; 
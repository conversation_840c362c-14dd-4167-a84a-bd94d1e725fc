// Mock wouter first, before any other imports
jest.mock('wouter', () => {
  const React = require('react');
  return {
    useLocation: () => ['/dashboard', jest.fn()],
    Link: ({ children, href }: any) => React.createElement('a', { href }, children),
  };
});

import { renderHook, waitFor } from '@testing-library/react';
import { useEmailActions } from '@/hooks/use-email-actions';
import apiClient from '@/lib/apiClient';
import { EmailAction } from '@/lib/constants';
import { useToast } from '@/hooks/use-toast';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import type { ReactNode } from 'react';

// Mock dependencies
jest.mock('@/lib/apiClient');
jest.mock('@/hooks/use-toast');
jest.mock('@/hooks/use-permission', () => ({
  usePermission: () => ({
    handlePermissionError: jest.fn().mockResolvedValue(false),
  }),
}));

// Mock AuthContext to avoid wouter dependency issues
jest.mock('@/context/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 1, email: '<EMAIL>' },
    isAuthenticated: true,
  }),
}));

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  __esModule: true,
  default: { currentUser: null },
  auth: { currentUser: null },
}));

// Mock logger
jest.mock('@/lib/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;
const mockedUseToast = useToast as jest.Mock;
const mockedToast = jest.fn();

// Setup a QueryClient wrapper for the hook
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  });
  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useEmailActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedUseToast.mockReturnValue({ toast: mockedToast });
  });

  describe('archiveEmail', () => {
    it('calls the archive endpoint and shows success toast', async () => {
      const messageId = 'msg-123';
      mockedApiClient.post.mockResolvedValue({ success: true });
      const wrapper = createWrapper();
      const { result } = renderHook(() => useEmailActions(), { wrapper });

      await result.current.archiveEmail(messageId);

      await waitFor(() => {
        expect(mockedApiClient.post).toHaveBeenCalledWith(`/api/emails/by-message-id/${messageId}/archive`);
        expect(mockedToast).toHaveBeenCalledWith({
          title: 'Success',
          description: 'Email archived successfully.',
        });
      });
    });

    it('shows an error toast when archive fails', async () => {
      const messageId2 = 'msg-456';
      const errorMessage = 'Archive failed';
      mockedApiClient.post.mockRejectedValue(new Error(errorMessage));
      const wrapper = createWrapper();
      const { result } = renderHook(() => useEmailActions(), { wrapper });

      await expect(result.current.archiveEmail(messageId2)).rejects.toThrow();

      await waitFor(() => {
        expect(mockedApiClient.post).toHaveBeenCalledWith(`/api/emails/by-message-id/${messageId2}/archive`);
        expect(mockedToast).toHaveBeenCalledWith({
          title: `Failed to ${EmailAction.ARCHIVE}`,
          description: errorMessage,
          variant: 'destructive',
        });
      });
    });
  });
}); 
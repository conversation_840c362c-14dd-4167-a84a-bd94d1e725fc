import type { DecodedIdToken } from "firebase-admin/auth";
import type { User } from "../../../shared/schema";

// ------------------------------------------------------------
// Mocks & Test Setup
// ------------------------------------------------------------

// Mock environment validator – supply FIREBASE_SERVICE_ACCOUNT so initializeFirebase() succeeds
jest.mock("../../../server/lib/environmentValidator", () => ({
  __esModule: true,
  getEnvVar: (key: string) => {
    if (key === "FIREBASE_SERVICE_ACCOUNT") {
      return JSON.stringify({
        project_id: "demo",
        private_key:
          "-----<PERSON><PERSON><PERSON> PRIVATE KEY-----\nABC\n-----<PERSON><PERSON> PRIVATE KEY-----\n",
        client_email: "<EMAIL>",
      });
    }
    if (key === "NODE_ENV") return "test";
    return "";
  },
}));

// Mock firebase-admin/app (initializeApp & cert)
jest.mock("firebase-admin/app", () => ({
  __esModule: true,
  initializeApp: jest.fn(() => ({})),
  cert: jest.fn(),
}));

// Storage mocks
jest.mock("../../../server/storage", () => ({
  storage: {
    getUserByFirebaseUid: jest.fn(),
    getUsersByEmail: jest.fn(),
    updateUser: jest.fn(),
    createUser: jest.fn(),
  },
}));

// firebase-admin/auth mock
const verifyIdTokenMock = jest.fn();
const getUserMock = jest.fn();

jest.mock("firebase-admin/auth", () => ({
  __esModule: true,
  getAuth: jest.fn(() => ({
    verifyIdToken: verifyIdTokenMock,
    getUser: getUserMock,
  })),
}));

// ------------------------------------------------------------
// Import module under test AFTER mocks
// ------------------------------------------------------------

import {
  initializeFirebase,
  verifyFirebaseToken,
  verifyIdTokenAndGetUser,
} from "../../../server/auth/firebase";
import { storage } from "../../../server/storage";

// Get references to mocked functions
const mockGetUserByFirebaseUid = jest.mocked(storage.getUserByFirebaseUid);
const mockGetUsersByEmail = jest.mocked(storage.getUsersByEmail);
const mockUpdateUser = jest.mocked(storage.updateUser);
const mockCreateUser = jest.mocked(storage.createUser);

// ------------------------------------------------------------
// Test Suite
// ------------------------------------------------------------

describe("Firebase Auth Service", () => {
  beforeAll(() => {
    initializeFirebase();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // ----------------------------------------------------------
  // ID Token Verification
  // ----------------------------------------------------------
  it("should verify a Firebase ID token and return decoded payload", async () => {
    const decoded: DecodedIdToken = {
      uid: "uid123",
      iss: "",
      exp: 0,
      iat: 0,
      aud: "",
      sub: "",
      auth_time: 0,
      provider_id: undefined,
      firebase: {} as any,
    } as DecodedIdToken;

    verifyIdTokenMock.mockResolvedValue(decoded);

    const result = await verifyFirebaseToken("TEST_TOKEN");

    expect(verifyIdTokenMock).toHaveBeenCalledWith("TEST_TOKEN", true);
    expect(result).toBe(decoded);
  });

  // ----------------------------------------------------------
  // UID-Based User Lookup
  // ----------------------------------------------------------
  it("should return user matched by Firebase UID and update profile", async () => {
    const decoded = {
      uid: "uidABC",
      email: "<EMAIL>",
      name: "Uid User",
      picture: "pic.jpg",
    } as any;
    verifyIdTokenMock.mockResolvedValue(decoded);

    const existingUser: User = {
      id: 1,
      email: decoded.email!,
      name: "Old",
      role: "user",
    } as User;

    mockGetUserByFirebaseUid.mockResolvedValue(existingUser);
    const updatedUser = { ...existingUser, name: decoded.name };
    mockUpdateUser.mockResolvedValue(updatedUser);

    const result = await verifyIdTokenAndGetUser("TOKEN_UID");

    expect(mockGetUserByFirebaseUid).toHaveBeenCalledWith(decoded.uid);
    expect(mockUpdateUser).toHaveBeenCalledWith(existingUser.id, {
      name: decoded.name,
      picture: decoded.picture,
    });
    expect(result).toBe(updatedUser);
    expect(mockCreateUser).not.toHaveBeenCalled();
  });

  // ----------------------------------------------------------
  // Email-Based Account Linking (duplicate prevention)
  // ----------------------------------------------------------
  it("should link Firebase UID to existing user found by email and not create duplicate", async () => {
    const decoded = {
      uid: "newUID",
      email: "<EMAIL>",
      name: "Shared Email",
      picture: null,
    } as any;
    verifyIdTokenMock.mockResolvedValue(decoded);

    mockGetUserByFirebaseUid.mockResolvedValue(null); // no uid match

    const existingByEmail: User = {
      id: 7,
      email: decoded.email!,
      name: "Existing",
      role: "user",
    } as User;
    mockGetUsersByEmail.mockResolvedValue([existingByEmail]);

    mockUpdateUser.mockResolvedValue({
      ...existingByEmail,
      firebaseUid: decoded.uid,
    });

    const result = await verifyIdTokenAndGetUser("TOKEN_EMAIL_LINK");

    // Should look up by email and update user
    expect(mockGetUsersByEmail).toHaveBeenCalledWith(decoded.email);
    expect(mockUpdateUser).toHaveBeenCalledWith(
      existingByEmail.id,
      expect.objectContaining({
        firebaseUid: decoded.uid,
        name: decoded.name,
      })
    );
    expect(result.id).toBe(existingByEmail.id);
    // Ensure no duplicate account was created
    expect(mockCreateUser).not.toHaveBeenCalled();
  });
});

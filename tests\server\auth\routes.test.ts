import express from 'express';
import request from 'supertest';

// ---------------------- Mocks ----------------------

// User fixture
const mockUser = { id: 1, email: '<EMAIL>', name: 'Tester' };

// Mock middleware: requireAuth & optionalAuth
jest.mock('../../../server/middleware/simpleAuth', () => {
  const requireAuth = (req: any, res: any, next: any) => {
    if (req.headers['x-auth'] === 'true') {
      req.user = mockUser;
      req.session = req.session || {};
      return next();
    }
    res.status(401).json({ code: 'AUTH_REQUIRED' });
  };

  const optionalAuth = (req: any, _res: any, next: any) => {
    if (req.headers['x-auth'] === 'true') {
      req.user = mockUser;
      req.session = req.session || {};
    }
    next();
  };
  return { __esModule: true, requireAuth, optionalAuth };
});

// Mock external services
jest.mock('../../../server/auth/google', () => ({
  __esModule: true,
  generateGoogleAuthUrl: jest.fn(() => 'https://auth.example.com'),
  handleGoogleCallback: jest.fn(() => ({ id: 1, email: '<EMAIL>' })),
}));

jest.mock('../../../server/auth/firebase', () => ({
  __esModule: true,
  verifyIdTokenAndGetUser: jest.fn(() => ({ id: 1, email: '<EMAIL>' })),
}));

// tokenService mocks
jest.mock('../../../server/services/tokenService', () => ({
  __esModule: true,
  default: {
    refreshTokens: jest.fn(() => ({ success: true })),
    parseTokens: jest.fn(() => ({ access_token: 'a', refresh_token: 'r' }))
  },
}));

// Silence logger
jest.mock('../../../server/lib/logger', () => ({ __esModule: true, default: { info: jest.fn(), warn: jest.fn(), error: jest.fn() } }));

// Mock environment validator to bypass checks
jest.mock('../../../server/lib/environmentValidator', () => ({ __esModule: true, getEnvVar: () => '' }));

// ---------------------- Import Router ----------------------
import authRouter from '../../../server/auth/routes';
import tokenService from '../../../server/services/tokenService';
import * as firebaseAuth from '../../../server/auth/firebase';
import * as googleAuth from '../../../server/auth/google';

// Get references to mocked functions
const refreshTokensMock = jest.mocked(tokenService.refreshTokens);
const parseTokensMock = jest.mocked(tokenService.parseTokens);
const verifyIdTokenAndGetUser = jest.mocked(firebaseAuth.verifyIdTokenAndGetUser);
const generateGoogleAuthUrl = jest.mocked(googleAuth.generateGoogleAuthUrl);
const handleGoogleCallback = jest.mocked(googleAuth.handleGoogleCallback);

// ---------------------- Test App ----------------------
function createTestApp() {
  const app = express();
  app.use(express.json());
  // Attach dummy csrfToken property for csrf route
  app.use((req: any, _res, next) => {
    req.csrfToken = 'test-csrf-token';
    req.session = req.session || { destroy: (cb: any) => cb() };
    next();
  });
  app.use('/api/auth', authRouter);
  return app;
}

// ---------------------- Tests ----------------------

describe('Auth Routes', () => {
  const app = createTestApp();

  describe('GET /api/auth/status', () => {
    it('returns 401 when unauthenticated', async () => {
      const res = await request(app).get('/api/auth/status');
      expect(res.status).toBe(401);
    });

    it('returns user data when authenticated', async () => {
      const res = await request(app).get('/api/auth/status').set('x-auth', 'true');
      expect(res.status).toBe(200);
      expect(res.body.data.user.id).toBe(mockUser.id);
    });
  });

  it('GET /api/auth/google initiates OAuth and returns authUrl', async () => {
    const res = await request(app)
      .get('/api/auth/google')
      .set('Accept', 'application/json');
    expect(res.status).toBe(200);
    expect(res.body.data.authUrl).toBe('https://auth.example.com');
    expect(generateGoogleAuthUrl).toHaveBeenCalled();
  });

  it('GET /api/auth/google/callback processes callback and redirects', async () => {
    const res = await request(app).get('/api/auth/google/callback').query({ code: 'code', state: 'state' });
    expect(handleGoogleCallback).toHaveBeenCalled();
    expect(res.status).toBe(302);
    expect(res.headers.location).toBe('/dashboard');
  });

  it('POST /api/auth/logout destroys session', async () => {
    const res = await request(app).post('/api/auth/logout').set('x-auth', 'true');
    expect(res.status).toBe(200);
    expect(res.body.message).toContain('Logged out');
  });

  it('POST /api/auth/firebase/verify verifies token', async () => {
    const res = await request(app).post('/api/auth/firebase/verify').send({ idToken: 'token' });
    expect(res.status).toBe(200);
    expect(verifyIdTokenAndGetUser).toHaveBeenCalledWith('token');
  });

  it('POST /api/auth/register registers user and establishes session', async () => {
    const res = await request(app).post('/api/auth/register').send({ idToken: 'token' });
    expect(res.status).toBe(200);
    expect(verifyIdTokenAndGetUser).toHaveBeenCalledWith('token');
  });

  it('POST /api/auth/refresh-token refreshes tokens', async () => {
    const res = await request(app).post('/api/auth/refresh-token').set('x-auth', 'true');
    expect(res.status).toBe(200);
    expect(refreshTokensMock).toHaveBeenCalled();
  });

  it('GET /api/auth/token-status returns token info', async () => {
    const res = await request(app).get('/api/auth/token-status').set('x-auth', 'true');
    expect(res.status).toBe(200);
    expect(parseTokensMock).toHaveBeenCalled();
  });

  it('POST /api/auth/fix-token-issues attempts token repair', async () => {
    const res = await request(app).post('/api/auth/fix-token-issues').set('x-auth', 'true');
    expect(res.status).toBe(200);
    expect(refreshTokensMock).toHaveBeenCalled();
  });

  it('GET /api/auth/csrf returns csrf token', async () => {
    const res = await request(app).get('/api/auth/csrf');
    expect(res.status).toBe(200);
    expect(res.body.csrfToken).toBe('test-csrf-token');
  });
}); 